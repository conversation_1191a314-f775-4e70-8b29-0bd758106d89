*** Settings ***
Resource    ${EXECDIR}/resources/serial_utility.resource
Library    BuiltIn    # Add this line to import BuiltIn library    

*** Variables ***
# EEPROM Setting Payload:
# [BRDH] [BRDL] [ADDRH] [ADDRL] [SIZE] [DATA0] [DATA1] [DATA2] [DATA3]

# Test payload that has a non-existent board ID - no board of id 0x4545
@{BAD_BOARD_ID}=    69    69    1    1    1    0

# Test payload that has an invalid byte size - expecting size of 1, 2, or 4 bytes but gets 16
@{BAD_MEMORY_SIZE}=    255    255    0    48    16    0    0    0    0

# Test payload that has an invalid byte size - expecting 4 bytes but only gets 1
@{BAD_MEMORY_LENGTH}=    255    255    0    48    4    0

*** Test Cases ***
Test EEPROM Read and Write Boolean
    [Timeout]    30 seconds
    [Tags]    EEPROM
    @{values}=    Create List    1    0
    FOR    ${value}    IN    @{values}
        Log    Writing value ${value} to Ingredient Mixing Enable - A6
        # Write setting and verify ACK
        ${WRITE_NAK}=    Set EEPROM Setting    Ingredient Mixing Enable - A6    ${value}
        Log    Write NAK: ${WRITE_NAK}
        Should Be True    not ${WRITE_NAK}
        
        # Read setting and verify ACK
        ${READ_NAK}    ${message}=    Get EEPROM Setting    Ingredient Mixing Enable - A6
        Should Be True    not ${READ_NAK}

        # Verify value
        Should Be Equal As Numbers   ${message}    ${value}
    END

Test EEPROM Read and Write float
    [Timeout]    30 seconds
    [Tags]    EEPROM
    @{values}=    Create List    10.0    15.0
    FOR    ${value}    IN    @{values}
        # Write setting and verify ACK
        ${WRITE_NAK}=    Set EEPROM Setting    Global Cleaner Rinse Speed    ${value}
        Should Be True    not ${WRITE_NAK}
        
        # Read setting and verify ACK
        ${READ_NAK}    ${message}=    Get EEPROM Setting    Global Cleaner Rinse Speed
        Should Be True    not ${READ_NAK}
        
        # Verify value
        Should Be Equal As Numbers   ${message}    ${value}
    END

Test EEPROM Read and Write uint8
    [Timeout]    30 seconds
    [Tags]    EEPROM
    @{values}=    Create List    14    16
    FOR    ${value}    IN    @{values}
        # Write setting and verify ACK
        ${WRITE_NAK}=    Set EEPROM Setting    Cleaning Concentrate Ratio    ${value}
        Should Be True    not ${WRITE_NAK}
        
        # Read setting and verify ACK
        ${READ_NAK}    ${message}=    Get EEPROM Setting    Cleaning Concentrate Ratio
        Should Be True    not ${READ_NAK}

        # Verify value
        Should Be Equal As Numbers   ${message}    ${value}
    END

Test EEPROM Read and Write uint16
    [Timeout]    30 seconds
    [Tags]    EEPROM
    @{values}=    Create List    69    50
    FOR    ${value}    IN    @{values}
        # Write setting and verify ACK
        ${WRITE_NAK}=    Set EEPROM Setting    Global Ingredient Sensor Active Threshold    ${value}
        Should Be True    not ${WRITE_NAK}
        
        # Read setting and verify ACK
        ${READ_NAK}    ${message}=    Get EEPROM Setting    Global Ingredient Sensor Active Threshold
        Should Be True    not ${READ_NAK}

        # Verify value
        Should Be Equal As Numbers   ${message}    ${value}
    END

Test Bad Board ID
    [Timeout]    30 seconds
    [Tags]    EEPROM
    # Attempt to Write setting
    Send Message    WRITE_EEPROM_SETTING    @{BAD_BOARD_ID}
    # Verify Write NAK
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK

Test Bad Memory Size
    [Timeout]    30 seconds
    [Tags]    EEPROM
    # Attempt to Write setting
    Send Message    WRITE_EEPROM_SETTING    @{BAD_MEMORY_SIZE}
    # Verify Write NAK
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK

Test Bad Memory Length
    [Timeout]    30 seconds
    [Tags]    EEPROM
    # Attempt to Write setting
    Send Message    WRITE_EEPROM_SETTING    @{BAD_MEMORY_LENGTH}
    # Verify Write NAK
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK