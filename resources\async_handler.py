from SerialLibrary import <PERSON>ial<PERSON>ib<PERSON>
from Decoder import Decoder
import os
import struct
from datetime import datetime, timedelta, timezone
import time

class AsyncHandler:
    """Handle asynchronous messages from the machine."""
    _async_handler_list = []
    def __init__(self):
        pass

    def add_handler(self, handler):
        """Add a message to be handled asynchronously."""
        print("Adding handler: " + handler)
        self._async_handler_list.append(handler)
    
    def remove_handler(self, handler):
        """Remove a message to be handled asynchronously."""
        print("Removing handler: " + handler)
        try:
            self._async_handler_list.remove(handler)
        except:
            pass
    def handle_async_packet(self, header, packet):
        """Handle asynchronous messages."""
        if header in self._async_handler_list:
            # Use the type_handlers dictionary to handle different types
            handler = async_frame_handlers.get(header, default_handler)
            print("Handling async message: " + header)
            return handler(packet)
        else:
            return None, None

def default_handler(packet):
    return None, None

def fatal_error_handle(packet):
    # Record fatal error, and send a fatal error exit response
    return "FATAL_ERROR_OVERRIDE", []

def sync_req_handle(packet):
    # Send current epoch time
    current_time = int(time.time())
    current_time_bytes = current_time.to_bytes(4, byteorder='big')

    now = datetime.now(timezone.utc).astimezone()
    tz_offset = now.utcoffset().total_seconds() / 3600
    tz_offset_bytes = int(tz_offset + 8).to_bytes(1, byteorder='big', signed=True)
    return "SYNC_REPLY", list(current_time_bytes) + list(tz_offset_bytes)

def refresh_schedule_req_handle(packet):
    # Send number of seconds until next top of hour
    now = datetime.now()
    next_hour = (now + timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)
    seconds_until = int((next_hour - now).total_seconds())
    seconds_until_bytes = seconds_until.to_bytes(2, byteorder='big')
    print(seconds_until_bytes)
    return "REFRESH_SCHEDULE_REPLY", list(seconds_until_bytes)

def board_records_req_handle(packet):
    # Future proof for handling board records. Timeout is OK for now
    pass

def refresh_lines_alert_handle(packet):
    # Return a refresh lines NAK to prevent refresh from happening, and allow master to exit refresh function
    return "REFRESH_LINES_NAK", []

def board_records_req_handle(packet):
    # Return a board records NAK to prevent refresh from happening, and allow master to exit refresh function
    return "BOARD_RECORDS_REPLY", [1,0xFF,0xFF]

# return {message, payload}
async_frame_handlers = {
    "FATAL_ERROR": fatal_error_handle,
    "SYNC_REQ": sync_req_handle,
    "REFRESH_SCHEDULE_REQ": refresh_schedule_req_handle,
    "BOARD_RECORDS_REQ": board_records_req_handle,
    "REFRESH_LINES_ALERT": refresh_lines_alert_handle,
    "BOARD_RECORDS_REQ": board_records_req_handle,
}

if __name__ == "__main__":
    async_handler = AsyncHandler()
    async_handler.add_handler("REFRESH_SCHEDULE_REQ")
    async_handler.handle_async("REFRESH_SCHEDULE_REQ", [1, 66])