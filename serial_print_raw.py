import serial
import argparse
import sys

def monitor_serial(port, baudrate=115200):
    try:
        ser = serial.Serial(port, baudrate)
        print(f"Connected to {port} at {baudrate} baud")

        try:
            while True:
                if ser.in_waiting > 0:
                    data = ser.read(ser.in_waiting)  # read all available bytes
                    # Print raw bytes in hex
                    print(' '.join(f'{b:02X}' for b in data))
        except KeyboardInterrupt:
            print("\nExiting...")
        finally:
            ser.close()
            print("Serial port closed")

    except serial.SerialException as e:
        print(f"Error: Could not open serial port: {e}")
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Monitor serial port')
    parser.add_argument('port', help='Serial port (e.g., COM1 or /dev/ttyUSB0)')
    parser.add_argument('-b', '--baudrate', type=int, default=115200,
                        help='Baud rate (default: 115200)')

    args = parser.parse_args()
    monitor_serial(args.port, args.baudrate)
