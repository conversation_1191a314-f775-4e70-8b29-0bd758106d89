*** Settings ***
Resource    ${EXECDIR}/resources/serial_utility.resource

*** Variables ***
# Test payload that has an invalid byte size - expecting 4 bytes but only gets 1
@{MASTER_BOARD_ID}=    65535


# Binary files being tested
${BINARY_PATH_}    ${EXECDIR}/resources/binary_files/binaries_under_test

# Standard test binary files
${BINARY_PATH_}    ${EXECDIR}/resources/binary_files/standard_binaries

*** Test Cases ***
Test Good OTA Update
    [Timeout]    2 minutes
    [Tags]    OTA
    File Transfer    ${EXECDIR}/resources/binary_files/binaries_under_test/master.bin
    Request Program    ${EXECDIR}/resources/binary_files/binaries_under_test/master.bin    @{MASTER_BOARD_ID}
    Sleep    5 seconds