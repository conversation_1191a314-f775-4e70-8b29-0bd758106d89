import serial
import sys
import glob
import serial.tools.list_ports
import time

class SerialLibrary:
    _serial_connection = None
    _decoder = None
    _async_handler = None

    end_sequence= [0x04, 0x014, 0x06, 0x09]

    def __init__(self, decoder_obj, async_handler_obj):
        self._decoder = decoder_obj
        self._async_handler = async_handler_obj

    def find_serial_port(self, keyword=None):
        """Search for an available COM port, optionally filtering by a keyword."""
        ports = list(serial.tools.list_ports.comports())
        for port in ports:
            if keyword and keyword.lower() in port.description.lower():
                return port.device
            elif not keyword:  # Return the first available port if no keyword is specified
                return port.device
        raise Exception("No suitable COM port found")

    def initialize_serial(self, baudrate=115200, keyword=None):
        """Initialize serial connection with auto-detected COM port."""

        if SerialLibrary._serial_connection is None:
            if sys.platform.startswith('win'):
                ports = ['COM%s' % (i + 1) for i in range(256)]
            elif sys.platform.startswith('linux') or sys.platform.startswith('cygwin'):
                # this excludes your current terminal "/dev/tty"
                ports = glob.glob('/dev/tty[A-Za-z]*')
            elif sys.platform.startswith('darwin'):
                ports = glob.glob('/dev/tty.*')
            else:
                raise EnvironmentError('Unsupported platform')
                        
            for port in ports:
                try:
                    if keyword and keyword.lower() in port.description.lower():
                        SerialLibrary._serial_connection = serial.Serial(port, baudrate, timeout=1)
                        if SerialLibrary._serial_connection is not None:
                            self.close_serial()
                        SerialLibrary._serial_connection = serial.Serial(port, baudrate, timeout=1)
                        print(f"Connected to {port}")
                        return SerialLibrary._serial_connection
                    elif not keyword:  # Try the first available port if no keyword is specified
                        SerialLibrary._serial_connection = serial.Serial(port, baudrate, timeout=1)
                        if SerialLibrary._serial_connection is not None:
                            self.close_serial()
                        SerialLibrary._serial_connection = serial.Serial(port, baudrate, timeout=1)
                        print(f"Connected to {port}")
                        return SerialLibrary._serial_connection
                except serial.SerialException:
                    print(f"Port {port} is in use. Trying next port...")
            raise Exception("No suitable COM port found or all ports are in use")
        return SerialLibrary._serial_connection
    
    def write_serial(self, command):
        """Send a command over serial."""
        if SerialLibrary._serial_connection:
            SerialLibrary._serial_connection.write(command.encode() + b'\n')

    def read_serial_byte(self):
        """Read a response from the serial device."""
        if SerialLibrary._serial_connection:
            byte = SerialLibrary._serial_connection.read(1)
            if not byte:
                return None
            return byte

    def close_serial(self):
        """Close the serial connection."""
        if SerialLibrary._serial_connection:
            SerialLibrary._serial_connection.close()
            SerialLibrary._serial_connection = None
    
    def reset_device(self):
        """Reset the device by toggling DTR."""
        if SerialLibrary._serial_connection:
            SerialLibrary._serial_connection.setDTR(False)
            time.sleep(0.1)
            SerialLibrary._serial_connection.setDTR(True)
    
    def send_data(self, payload):
        """Send data to the device."""
        if SerialLibrary._serial_connection:
            SerialLibrary._serial_connection.write(payload)

    def send_packet(self, message = "", payload = [], include_end_bytes = True):
        """Send a command over serial."""
        if message == "":
            return
        starting_bytes = self._decoder.get_starting_bytes(message)
        if starting_bytes is None:
            return
        command = (starting_bytes + payload)
        if SerialLibrary._serial_connection:
            if include_end_bytes:
                SerialLibrary._serial_connection.write(command + (self.end_sequence))
            else:
                SerialLibrary._serial_connection.write(command)
    
    def read_packet(self, timeout=5):
        """Read a response from the serial device."""
        if SerialLibrary._serial_connection:
            buffer = []
            current_time = time.time() # Get the current time
            while(1):
                byte = self.read_serial_byte()
                if byte is not None:
                    byte = int.from_bytes(byte, 'big')
                    buffer.append(byte)
                    if buffer[-len(self.end_sequence):] == self.end_sequence:
                        return buffer
            return None

    def await_packet(self, command_list, timeout=60):
        if command_list is str:
            command_list = [command_list]
        current_time = time.time()
        """Wait for a specific packet."""
        while(1):
            packet = self.read_packet()
            try:
                if self.decode_packet(packet) in command_list:
                    return packet
                elif packet:
                    header, payload = self._async_handler.handle_async_packet(self._decoder.decode_packet(packet), packet)
                    if header:
                        self.send_packet(header, payload)
            except:
                pass
        return None

    def decode_packet(self, packet):
        """Decodeos a packet into a string."""
        return self._decoder.decode_packet(packet)
