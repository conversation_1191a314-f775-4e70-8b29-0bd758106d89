# Utility Class imports
from SerialLibrary import <PERSON><PERSON><PERSON><PERSON><PERSON>
from Decoder import Decoder
from async_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from OTA import OTA_handler

# Generic Library imports
import os
import time
import struct

delete_files = ["serial_headers", "eeprom_headers"]

class MachineAppUtility:
    current_board_list = []

    def __init__(self):
        self.decoder = Decoder()
        self.async_handler = AsyncHandler()
        self.serial_library = SerialLibrary(self.decoder, self.async_handler)
        self.ota_handler = OTA_handler(self.serial_library)

    def roll_call(self):
        self.send_packet("ROLL_CALL_REQ")
        return_packet = self.await_packet("ROLL_CALL_REPLY", timeout=20)
        self.current_board_list.append(0xffff)
        try:
            for index in range(3,return_packet[2],2):
                value_bytes = return_packet[index:index+1]
                self.current_board_list.append(int.from_bytes(value_bytes, byteorder='big'))
        except:
            pass

    def ota_file_transfer(self, binary_file_path):
        """Transfer a file to the master"""
        NAK_resp = self.ota_handler.OTA_file_transfer(binary_file_path)
        if NAK_resp:
            print("Error sending file to master!")
            return NAK_resp
        print("File successfully send to master!")
        return (0)

    def ota_board_request(self, binary_file_path, boards):
        """Send a program request to the master for a file that has been transferred"""
        NAK_resp = self.ota_handler.OTA_program_board(binary_file_path, boards)
        if NAK_resp:
            print(f"Error programming board(s): {boards}")
            return NAK_resp
        print(f"Board(s) successfully programed: {boards}!")
        return (0)
        
    def update_csv(self):
        """Update the CSV files with EEPROM and serial headers."""
        self.decoder.update_headers()
    
    def add_async_handler(self, handler):
        """Add a message to be handled asynchronously."""
        if type(handler) is list:
            for h in handler:
                self.async_handler.add_handler(h)
        else:
            self.async_handler.add_handler(handler)
    
    def remove_async_handler(self, handler):
        """Remove a message to be handled asynchronously."""
        if type(handler) is list:
            for h in handler:
                self.async_handler.remove_handler(h)
        else:
            self.async_handler.remove_handler(handler)

    def initialize_serial(self, baudrate = 115200, keyword=None):
        """Initialize serial connection with auto-detected COM port."""
        self.serial_library.initialize_serial(baudrate, keyword)

    def send_data(self, payload):
        """Send data to the device."""
        if SerialLibrary._serial_connection:
            self.serial_library.send_data(payload)

    def send_packet(self, message = "", payload = [], include_end_bytes = True):
        """Send a command over serial."""
        return self.serial_library.send_packet(message, payload, include_end_bytes)

    def read_packet(self, timeout=5):
        """Read a response from the serial device."""
        if SerialLibrary._serial_connection:
            return self.serial_library.read_packet(timeout)

    def await_packet(self, command_list, timeout=60):
        return self.serial_library.await_packet(command_list,timeout)

    def decode_packet(self, packet):
        """Decodes a packet into a string."""
        return self.serial_library.decode_packet(packet)

    def close_serial(self):
        """Close the serial connection."""
        if SerialLibrary._serial_connection:
            SerialLibrary._serial_connection.close()
            SerialLibrary._serial_connection = None

    def reset_device(self):
        """Reset the device by toggling DTR."""
        if SerialLibrary._serial_connection:
            SerialLibrary._serial_connection.setDTR(False)
            time.sleep(0.1)
            SerialLibrary._serial_connection.setDTR(True)
    
    def archive_data(self):
        script_dir = os.path.dirname(__file__)
        for filename in delete_files:
            file_path_old = os.path.join(script_dir, filename+'_prev.csv')
            file_path_new = os.path.join(script_dir, filename+'.csv')
            if os.path.exists(file_path_old):
                os.remove(file_path_old)
            if os.path.exists(file_path_new):
                os.rename(file_path_new,file_path_old)

    def read_eeprom_setting(self, setting_name):
        """Read an EEPROM setting."""
        setting_name, Board_ID, EEPROM_Address, Size, Type, default_value = self.decoder.get_eeprom_setting(setting_name)
        Board_ID_bytes = int(Board_ID).to_bytes(2, byteorder='big')
        EEPROM_Address_bytes = int(EEPROM_Address, 16).to_bytes(2, byteorder='big')
        payload = list(Board_ID_bytes) + list(EEPROM_Address_bytes) + [int(Size)]
        self.send_packet("READ_EEPROM_SETTING_REQ", payload) 
        return_packet = self.await_packet(["READ_EEPROM_SETTING_REPLY","READ_EEPROM_SETTING_NAK"])
        if return_packet is None:
            return True, None
        if self.decode_packet(return_packet) == "READ_EEPROM_SETTING_NAK":
            setting_NAK = True
            return setting_NAK, return_packet
        else:
            setting_NAK = False
        if Type == "float":
            return setting_NAK, struct.unpack('f', bytearray(return_packet[7:7+int(Size)]))[0]
        else:
            value_bytes = return_packet[7:7+int(Size)]
            return setting_NAK, int.from_bytes(value_bytes, byteorder='big')
    
    def get_random_eeprom_setting(self):
        """Get a random EEPROM setting."""
        setting_name, Board_ID, EEPROM_Address, Size, Type, default_value = self.decoder.get_eeprom_setting("",random=True)
        return setting_name, default_value

    def write_eeprom_setting(self, setting_name, value=0):
        """Read an EEPROM setting."""
        setting_name, Board_ID, EEPROM_Address, Size, Type, default_value = self.decoder.get_eeprom_setting(setting_name)
        Board_ID_bytes = int(Board_ID).to_bytes(2, byteorder='big')
        EEPROM_Address_bytes = int(EEPROM_Address, 16).to_bytes(2, byteorder='big')

        if Type == "float":
            value = float(value)
            value_bytes = bytearray(struct.pack("f", value))
        else:
            value_bytes = int(value).to_bytes(int(Size), byteorder='big')
        payload = list(Board_ID_bytes) + list(EEPROM_Address_bytes) + [int(Size)] + list(value_bytes)
        self.send_packet("WRITE_EEPROM_SETTING", payload)
        rx_packet = self.await_packet(["WRITE_EEPROM_SETTING_ACK", "WRITE_EEPROM_SETTING_NAK"])
        if self.decode_packet(rx_packet) == "WRITE_EEPROM_SETTING_NAK":
            setting_NAK = True
        else:
            setting_NAK = False
        return setting_NAK
    
    def sanatize_eeprom_settings(self):
        """Sanatize EEPROM settings based on board list."""
        self.decoder.sanatize_settings(self.current_board_list)

if __name__ == "__main__":
    machine_app = MachineAppUtility()
    machine_app.initialize_serial()
    machine_app.add_async_handler("FATAL_ERROR")
    machine_app.roll_call()
    machine_app.sanatize_eeprom_settings()