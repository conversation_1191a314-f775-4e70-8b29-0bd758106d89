*** Settings ***
Library    MachineAppUtility.py    WITH NAME    GlobalMachineAppUtility

*** Variables ***
${BAUDRATE}       115200
${MACHINE_APP_UTILITY}    ${None}

*** Keywords ***
Initialize Machine App Utility
    [Documentation]    Initialize the singleton instance of MachineAppUtility
    ${utility}=    Get Library Instance    GlobalMachineAppUtility
    Set Global Variable    ${MACHINE_APP_UTILITY}    ${utility}

Open Serial Connection
    [Documentation]    Open serial connection only once if not already open
    ${is_open}=    Get Variable Value    ${CONNECTION_OPEN}    ${False}
    Run Keyword If    not ${is_open}    Call Method    ${MACHINE_APP_UTILITY}    initialize_serial    ${BAUDRATE}
    Set Global Variable    ${CONNECTION_OPEN}    ${True}

Close Serial Connection
    [Documentation]    Close serial connection at the end of the test run
    Run Keyword If    ${CONNECTION_OPEN}    Call Method    ${MACHINE_APP_UTILITY}    close_serial
    Set Global Variable    ${CONNECTION_OPEN}    ${False}

Parse Incoming Packet
    [Documentation]    Parse incoming packet and log the data
    ${data}=    Call Method    ${MACHINE_APP_UTILITY}    read_packet
    RETURN    ${data}

Decode Received Packet
    [Documentation]    Decode packet and log the message
    [Arguments]    ${packet}
    ${message}=    Call Method    ${MACHINE_APP_UTILITY}    decode_packet    ${packet}
    RETURN    ${message}

Send Message
    [Documentation]    Send packet to the device
    [Arguments]    ${message}    @{payload}
    ${int_payload}=    Evaluate    [int(x) for x in @{payload}]
    Call Method    ${MACHINE_APP_UTILITY}    send_packet    ${message}    ${int_payload}

Await Message
    [Documentation]    Await message from the device
    [Arguments]    @{message}
    ${packet}=    Call Method    ${MACHINE_APP_UTILITY}    await_packet    ${message}
    RETURN    ${packet}

Set EEPROM Setting
    [Documentation]    Set EEPROM settings on the device
    [Arguments]    ${setting_name}    ${value}
    ${WRITE_NAK}=    Call Method    ${MACHINE_APP_UTILITY}    write_eeprom_setting    ${setting_name}    ${value}

Get EEPROM Setting
    [Documentation]    Get EEPROM settings from the device
    [Arguments]    ${setting_name}
    ${READ_ACK}    ${settings}=    Call Method    ${MACHINE_APP_UTILITY}    read_eeprom_setting    ${setting_name}
    RETURN    ${READ_ACK}    ${settings}

New Async Handler
    [Documentation]    Add async handler to the device
    [Arguments]    ${handler}
    Call Method    ${MACHINE_APP_UTILITY}    add_async_handler    ${handler}

Delete Async Handler
    [Documentation]    Delete async handler from the device
    [Arguments]    ${handler}
    Call Method    ${MACHINE_APP_UTILITY}    remove_async_handler    ${handler}

Get Random Setting
    [Documentation]    Get random EEPROM setting from the device
    ${setting}    ${value}=    Call Method    ${MACHINE_APP_UTILITY}    get_random_eeprom_setting
    RETURN    ${setting}    ${value}

Download Headers
    [Documentation]    Download Serial and EEPROM headers
    Call Method    ${MACHINE_APP_UTILITY}    update_csv

Archive Testing Data
    [Documentation]    Archive testing data at the end of the test run
    Call Method    ${MACHINE_APP_UTILITY}    archive_data

Perform Roll Call
    [Documentation]    Perform roll call to ensure all devices are connected
    Call Method    ${MACHINE_APP_UTILITY}    roll_call

Clean Database
    [Documentation]    Clean eeprom database based on roll call
    Call Method    ${MACHINE_APP_UTILITY}    sanatize_eeprom_settings

File Transfer
    [Documentation]    Send a file to the master
    [Arguments]    ${BINARY_LOCATION}
    Call Method    ${MACHINE_APP_UTILITY}    ota_file_transfer    ${BINARY_LOCATION}

Request Program
    [Documentation]    Request a board to update
    [Arguments]    ${BINARY_LOCATION}    @{BOARD_IDS}
    Call Method    ${MACHINE_APP_UTILITY}    ota_board_request    ${BINARY_LOCATION}    @{BOARD_IDS}

Ping Master
    [Documentation]    Ping master to ensure it is connected
    [Arguments]    ${iterations}=5
    FOR    ${index}    IN RANGE    ${iterations}
        Call Method    ${MACHINE_APP_UTILITY}    send_message    HEARTBEAT_REQ
        ${response}=    Call Method    ${MACHINE_APP_UTILITY}    await_message    HEARTBEAT_REPLY
        Should Not Be Equal    ${response}    ${None}    Failed to receive response on iteration ${index + 1}
        Log    Successfully received response on iteration ${index + 1}
    END

Reset Master
    [Documentation]    Reset master to ensure it is in a known state
    Call Method    ${MACHINE_APP_UTILITY}    reset_device

Send Data
    [Documentation]    Send data to the master
    [Arguments]    @{payload}
    Call Method    ${MACHINE_APP_UTILITY}    send_data    @{payload}