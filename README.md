# EEPROM Testing Suite

Automated testing suite for EEPROM settings validation using Robot Framework.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Running Tests](#running-tests)
- [Test Structure](#test-structure)
- [Adding New Tests](#adding-new-tests)
- [Utilities](#utilities)
- [Contributing](#contributing)

## Prerequisites

- Python 3.x
- Robot Framework
- PySerial library
- Visual Studio Code (recommended)

Install required packages:
```bash
pip install robotframework
pip install pyserial
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-repo/testing_suite.git
cd testing_suite
```

2. Set up your development environment:
   - Open the project in Visual Studio Code
   - Install the Robot Framework Language Server extension

## Running Tests

Execute all tests from the command line:
```bash
robot --pythonpath . tests
```

Run specific test files:
```bash
robot --pythonpath . tests/General/EEPROM_WRITE.test
```

Run tests with specific tags:
```bash
robot --pythonpath . --include {Tag} tests/
```

## Test Structure

```
testing_suite/
├── resources/
│   ├── serial_utility.resource   # Common keywords and utilities
│   ├── MachineAppUtility.py      # Machine application interface
│   ├── Decoder.py                # Message decoder
│   ├── async_handler.py          # Async message handlers
│   └── SerialLibrary.py          # Serial communication
└── tests/
    ├── __init__.robot            # Suite initialization
    └── General/                  # All `General` category tests
        └── EEPROM_WRITE.robot          # EEPROM Write test cases
    └── Calibration/              # All `Calibration` category tests
    └── Log Dumps/                # All `Log Dumps` category tests
    └── Maintenance/              # All `Maintenance` category tests
    └── Sleep/                    # All `Sleep` category tests
    └── Testing/                  # All `Testing` category tests
    └── Firmware Updates/         # All `Firmware Updates` category tests
    └── Dispensing/               # All `Dispensing` category tests

```

## Adding New Tests

### 1. Create a New Test File

Example EEPROM structure:
```robotframework
*** Settings ***
Resource    ${EXECDIR}/resources/serial_utility.resource    # import path for all serial utility resources

*** Variables ***
# Example of a list
@{TEST_VALUES}=    value1    value2    value3   

# Example of a single variable
${Value}= value

*** Test Cases ***
Test New EEPROM Setting
    [Documentation]    Test reading and writing a new EEPROM setting
    [Timeout]    30 seconds     # A timeout value that will automatically FAIL a test if it has not finished
    @{values}=    Create List    ${TEST_VALUES}
    FOR    ${value}    IN    @{values}
        write_eeprom_setting    Setting Name    ${value}
        ${message}=    Get EEPROM Setting    Setting Name
        Should Be Equal As Numbers    ${message}    ${value}
    END
```

### 2. Add Test Cases

Basic test case template:
```robotframework
Test Case Name
    [Documentation]    Describe what the test does
    [Tags]    tag1    tag2      
    [Timeout]    30 seconds
    # Test steps here
```

### 3. Common Test Patterns

Reading and writing EEPROM values:
```robotframework
Test Name
    [Timeout]    30 seconds     # Define a maximum runtime for this test
    
    ** INSERT YOUR TEST CASES HERE: **

```

Testing error conditions:
```robotframework
Test Error Condition
    [Timeout]    30 seconds
    Send Message    WRITE_EEPROM_SETTING    @{ERROR_PAYLOAD}
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK
```

## Utilities

### Serial Communication Utility functions
- `Write Eeprom Setting`: Write a value to an EEPROM setting
    - Syntax: `Write Eeprom Setting [Setting Name per Notion DB] [Value]`
    - Example:
        ```robotframework
        Test Example Write EEPROM Setting
            Write Eeprom Setting    Global Water Rinse Speed   10.0
        ```
- `Get EEPROM Setting`: Read a value from an EEPROM setting
    - Syntax: `${value}= Get Eeprom Setting [Setting Name per Notion DB]`
    - Example:
        ```robotframework
        Test Example Read EEPROM Setting
            ${value}= Get Eeprom Setting Global Water Rinse Speed
            log ${value}
        ```
- `Send Message`: Send a raw message to the device
    - Syntax: `Send Message [Serial Header] [payload]`
    - Example:
        ```robotframework
        Test Example Send Message
            # Create a payload represented by the list below
            @{EXAMPLE_PAYLOAD}=     1   2   3
            Send Message    SYNC_REPLY  @{EXAMPLE_PAYLOAD}
        ```
- `Await Message`: Wait for a specific message response
    - Syntax: `@{response}= Await Message [Serial Header]`
    - Example:
        ```robotframework
        Test Example Await Message
            @{Incoming_Packet}= Await Message    WRITE_EEPROM_SETTING_NAK
        ```

### Async Handlers

There are some messages that can require the machine app to asynchronously respond to alerts/requests, which can cause issues while testing. One example is `REFRESH_LINES_ALERT` which is sent when any ingredients needs to be refreshed. This can cause an interruption in firmware responses creating a timeout on tests.

The easiest solution for handling these incoming messages is creating asynchronous handlers that respond as if they are the machine app responding. These handlers are meant to perform the most basic response possible, and they are not intended for testing the actual functionality of a feature (beyond the expected, typical response). They can be turned on/off for specific function testing using the following keywords:

- `New Async Handler`: Add an asynchronous message header to the list of messages to service
    - Syntax: `New Async Handler [Serial Header]`
    - Example:
        ```robotframework
        Test Adding Async Handler
            New Async Handler    SYNC_REQ
        ```
- `Delete Async Handler`: Wait for a specific message response
    - Syntax: `Delete Async Handler [Serial Header]`
    - Example:
        ```robotframework
        Test Removing Async Handler
            Delete Async Handler    REFRESH_LINES_ALERT
        ```

Whenever the testing suite is run, there is a list of initial tasks that are run by the testing suite itself. One of those steps is creating the initial list of asynchronous handlers. All startup steps are kept in `tests/__init__.robot` and can be edited as required. Currently, all standard asynchrous messages handled by the default setup are:
- `SYNC_REQ`
    - Handling: `SYNC_REPLY` header with the current epoch and timezone are sent per serial spec
- `REFRESH_SCHEDULE_REQ`
    - Handling: `REFRESH_SCHEDULE_REPLY` header with the number of seconds until top of the hour per serial spec
- `REFRESH_LINES_ALERT`
    - Handling: `REFRESH_LINES_NAK` header to prevent refresh and force master back to main execution loop


#### Adding NEW Async Handlers

Add new async handlers in `resources/async_handler.py`
```python
def serial_message_header_handler(packet):
    # Handle the packet

    # return the response message [Header], [Payload]
    return "RESPONSE_HEADER", payload_list
```

As an example, say I want to add a handler for a message header `FAKE_EXAMPLE`. The response should be a `FAKE_RESPONSE_ACK` with a payload of [1, 2, 3].

```python
def fake_example_handler(packet):
    # Handle the packet
    payload = [1, 2, 3]

    # return the response message [Header], [Payload]
    return "FAKE_RESPONSE_ACK", payload
```

A real example for `SYNC_REQ`:

```python
def sync_req_handle(packet):
    # Get current epoch time
    current_time = int(time.time())
    current_time_bytes = current_time.to_bytes(4, byteorder='big')

    # Get the current timezone
    now = datetime.now(timezone.utc).astimezone()
    tz_offset = now.utcoffset().total_seconds() / 3600
    tz_offset_bytes = int(tz_offset + 8).to_bytes(1, byteorder='big', signed=True)

    # Return the correct response
    return "SYNC_REPLY", list(current_time_bytes) + list(tz_offset_bytes)
```


### Testing Guidelines
- Each test should be independent of one another and have any relevant setup steps within the test itself
    - This can include setting specific EEPROM settings, restarting the master PCB, etc.
- Handle cleanup in test teardown
    - If you disable any default async handlers, re-enable them
- Test cases should include positive and negative results
    - Do not limit tests to cases that are handled by firmware currently 
- Add `[Timeout]` to every test so we cannot hand indefinitely when testing