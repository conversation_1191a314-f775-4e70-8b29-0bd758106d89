import pandas as pd
import sys
import os

class OTA_handler:
    _serial_obj = None

    def __init__(self, serial_obj):
        self._serial_obj = serial_obj

    def OTA_file_transfer(self, binary_file_path):
        """Send a binary file to the master. Return 0 on success"""

        # Send binary file to master
        NAK_rec, transfer_buffer_size = self._start_file_transfer(binary_file_path)
        if NAK_rec != 0:
            print("Error in Starting File Transfer!")
            return NAK_rec

        # Send binary file to master
        print(f"Sending {binary_file_path}.bin to master...")
        NAK_rec = self._file_transfer_data(binary_file_path, transfer_buffer_size)

        if NAK_rec != 0:
            print("Error in Data Transfer!")
            return NAK_rec
        
        print("File successfully sent to master!")
        return (0)
    
    def OTA_program_board(self, binary_file_path, target_boards):
        """Request a board to program using a specific binary file. Return 0 on success"""

        file_name = os.path.basename(binary_file_path).rstrip('.bin')
        
        # Convert target_boards to list if it's not already
        if not isinstance(target_boards, list):
            target_boards = [target_boards]
        
        # Convert all items to integers
        target_boards = [int(board) for board in target_boards]
        
        # Send a program request
        for target_board in target_boards:
            if target_board == 0xffff:
                self._serial_obj.send_packet("SELF_PROGRAM_REQ")
                rec_packet = self._serial_obj.await_packet(["SELF_PROGRAM_ACK", "SELF_PROGRAM_NAK"])
                if self._serial_obj.decode_packet(rec_packet) != "SELF_PROGRAM_ACK":
                    print("Error, got a NAK during Self Programming")
                    return (1)
            else:
                payload = self._get_program_data(file_name, target_board)
                self._serial_obj.send_packet("PROGRAM_REQ", payload)
                rec_packet = self._serial_obj.await_packet(["PROGRAM_ACK", "PROGRAM_NAK"])
                if self._serial_obj.decode_packet(rec_packet) != "PROGRAM_ACK":
                    print("Error, got a NAK during Programming")
                    return (1)
        print (f"Successfully programed Board ID(s): {target_boards}")
        return (0)

    def _start_file_transfer(self, binary_file_path):
        
        # Add file size, name, and crc
        payload = self._get_transfer_data(binary_file_path)
        
        self._serial_obj.send_packet("TRANSFER_START", payload)

        rec_packet = self._serial_obj.await_packet("TRANSFER_READY")
        if self._serial_obj.decode_packet(rec_packet) != "TRANSFER_READY":
            print("Error, got a NAK during TRANSFER_START")
            return (1, 0)
        
        # extract the buffer size from master and subtract 2 to account for category and header bytes
        transfer_buffer_size = int.from_bytes(rec_packet[2:4], "big")

        return 0, transfer_buffer_size

    def _get_transfer_data(self, binary_file_path):
        payload = []
        
        # Add file size
        size = os.path.getsize(binary_file_path)
        payload.extend(size.to_bytes(4,"big"))

        # Add file name
        file_name = os.path.basename(binary_file_path)
        for i in file_name:
            payload.append(ord(i))

        payload.append(0)   # Null character

        payload.extend(self._crc_calc(binary_file_path))

        return payload  # Add this return statement

    def _get_program_data(self, file_name, board_id): 
        payload = board_id.to_bytes(2,"big")
        for i in file_name:
            payload.append(ord(i))
        # Null Terminator
        payload.append(0)

        return payload

    def _file_transfer_data(self, binary_file_path, buffer_size):
        count = 0
        payload = []
        with open(binary_file_path, 'rb') as file:
            bytes = file.read()
            for byte in bytes:
                if(count == buffer_size):
                    self._serial_obj.send_packet("TRANSFER_DATA",payload, False)
                    rec_packet = self._serial_obj.await_packet(["TRANSFER_READY", "TRANSFER_ACK", "TRANSFER_NAK"])
                    if self._serial_obj.decode_packet(rec_packet) not in ["TRANSFER_READY", "TRANSFER_ACK"]:
                        print("Error, got a NAK during TRANSFER_DATA")
                        return (1)
                    payload = []
                    count = 0
                payload.append(byte)
                count+=1
            # Handle Bin file that is not multiple of 256 bytes
            if count:
                self._serial_obj.send_packet("TRANSFER_DATA",payload, False)
                rec_packet = self._serial_obj.await_packet(["TRANSFER_READY", "TRANSFER_ACK", "TRANSFER_NAK"])
                if self._serial_obj.decode_packet(rec_packet) not in ["TRANSFER_READY", "TRANSFER_ACK"]:
                    print("Error, got a NAK during TRANSFER_DATA")
                    return (1)
        return (0)
    
    def _crc_calc(self, binary_file_path):
        crc = 0
        with open(binary_file_path, 'rb') as file:
            bytes = file.read()
            for byte in bytes:
                crc = self._crc16_update(crc, byte)

        return(crc.to_bytes(2,"big"))
    
    def _crc16_update(self, crc, a):
        crc ^= a
        crc &= 0xFFFF
        for i in range(0,8):
            if (crc & 1):
                crc = (crc >> 1) ^ 0xA001
                crc &= 0xFFFF
            else:
                crc = (crc >> 1)
                crc &= 0xFFFF
        return crc