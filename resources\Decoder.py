import pandas as pd
import os
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import os.path
import csv
import random
from notion_client import Client

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/spreadsheets.readonly"]

# The ID and range of a sample spreadsheet.
SAMPLE_SPREADSHEET_ID = "19UrqdK5_1h2b_YaS6NBxwJe0iIfkqr8rKnMPZr3iAhY"

# Get the Notion integration token from the environment variables
notion_token = "**************************************************"

# Initialize the Notion client
notion = Client(auth=notion_token)

class Decoder:

    def __init__(self):
        """Initialize the Decoder with empty DataFrames and update headers."""
        self._serial_df = None
        self._eeprom_df = None
        self.update_headers()

    def update_headers(self):
        self.update_serial_headers()
        self.update_eeprom_settings()
        # Get the directory of the current script
        script_dir = os.path.dirname(__file__)
        # Construct the full path to the CSV file
        serial_csv_path = os.path.join(script_dir, 'serial_headers.csv')
        eeprom_csv_path = os.path.join(script_dir, 'eeprom_headers.csv')
        # Read the CSV file into a DataFrame
        self._serial_df = pd.read_csv(serial_csv_path)
        self._eeprom_df = pd.read_csv(eeprom_csv_path)

    def decode_packet(self, packet):
        """Decode a packet into a string."""
        decoded = ''
        for header in self._serial_df.iterrows():
            try:
                high_byte = int(header[1]["High Byte (Category)"], 16)
                low_byte = int(header[1]["Low Byte (Message)"], 16)
                if packet[0] == high_byte and packet[1] == low_byte:
                    return header[1]["Message"]
            except:
                pass
        return "Unknown"

    def get_starting_bytes(self, command):
        """Get the starting bytes for a given command."""
        for header in self._serial_df.iterrows():
            if header[1]["Message"] == command:
                return [int(header[1]["High Byte (Category)"], 16), int(header[1]["Low Byte (Message)"], 16)]
        return None

    def sanatize_settings(self, board_list):
        """Sanatize EEPROM settings based on board list."""
        for setting in self._eeprom_df.iterrows():
            try:
                if int(setting[1]["Board ID"]) not in board_list:
                    self._eeprom_df.drop(setting[0], inplace=True)
            except:
                self._eeprom_df.drop(setting[0], inplace=True)
    
    def get_eeprom_setting(self,setting_name, random=False):
        """Get the EEPROM setting."""
        if random:
            # Get a random row from the DataFrame
            random_row = self._eeprom_df.sample(n=1).iloc[0]
            return (random_row["Friendly Name (Gen 2)"],
                    random_row["Board ID"], 
                    random_row["EEPROM Address"], 
                    random_row["Size (bytes)"], 
                    random_row["Type"],
                    random_row["Default Value (Alpha)"])
        else:
            # Find specific setting by name
            setting = self._eeprom_df[self._eeprom_df["Friendly Name (Gen 2)"] == setting_name]
            if not setting.empty:
                row = setting.iloc[0]
                return (row["Friendly Name (Gen 2)"],
                        row["Board ID"],
                        row["EEPROM Address"],
                        row["Size (bytes)"],
                        row["Type"],
                        row["Default Value (Alpha)"])
        return None

    def update_serial_headers(self):
        """Shows basic usage of the Sheets API.
        Prints values from a sample spreadsheet.
        """
        creds = None
        script_dir = os.path.dirname(__file__)
        credential_path = os.path.join(script_dir, 'credentials.json')
        token_path = os.path.join(script_dir, 'token.json')
        csv_path = os.path.join(script_dir, 'serial_headers.csv')
        if os.path.exists(csv_path):
            return
        # The file token.json stores the user's access and refresh tokens, and is
        # created automatically when the authorization flow completes for the first
        # time.
        if os.path.exists(token_path):
            creds = Credentials.from_authorized_user_file(token_path, SCOPES)
        # If there are no (valid) credentials available, let the user log in.
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    credential_path, SCOPES
                )
                creds = flow.run_local_server(port=0)
                # Save the credentials for the next run
            with open("token.json", "w") as token:
                token.write(creds.to_json())

        try:
            service = build("sheets", "v4", credentials=creds)

            # Call the Sheets API
            sheet = service.spreadsheets()
            result = (
                sheet.values()
                .get(spreadsheetId=SAMPLE_SPREADSHEET_ID, range="Messages!A2:Z")
                .execute()
            )
            values = result.get("values", [])

            if not values:
                print("No data found.")
                return

            # Save the data to a CSV file
            with open(csv_path, 'w', newline='') as csvfile:
                csvwriter = csv.writer(csvfile)
                for row in values:
                    csvwriter.writerow(row)
            
        except HttpError as err:
            print(err)
    def update_eeprom_settings(self):
        """Shows basic usage of the Sheets API.
        Prints values from a sample spreadsheet.
        """
        script_dir = os.path.dirname(__file__)
        csv_path = os.path.join(script_dir, 'eeprom_headers.csv')
        if os.path.exists(csv_path):
            return
        general_headers = ["Friendly Name (Gen 2)","Board ID","EEPROM Address","Type","Private?","Size (bytes)", "Current Status","Default Value (Alpha)"]

        # Replace with your database ID
        database_id = '7ee2674fa29041cea4be578149793733'

        all_entries = self.fetch_all_database_entries(database_id)

        with open(csv_path, mode="w+",encoding='utf-8', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(general_headers)
            for setting in all_entries:
                try:
                    properties = setting.get('properties', {})
                    entry_data = {}
                    for prop_name, prop_values in properties.items():
                        entry_data[prop_name] = self.extract_value(prop_values)
                    csv_data = []
                    for item in general_headers:
                        try:
                            csv_data.append(entry_data[item])
                        except:
                            csv_data.append("?")
                    if entry_data["Current Status"] == "Production":
                        writer.writerow(csv_data)
                except:
                    pass
        

    def fetch_all_database_entries(self, database_id):
        results = []
        next_cursor = None

        while True:
            response = notion.databases.query(
                **{
                    "database_id": database_id,
                    "start_cursor": next_cursor
                }
            )
            results.extend(response['results'])
            next_cursor = response.get('next_cursor')
            if not response['has_more']:
                break

        return results

    def extract_value(self,prop_values):
        if not prop_values:
            return None
        prop_type = prop_values.get('type')
        if prop_type == 'title':
            return prop_values.get('title', [{}])[0].get('plain_text', '') if prop_values.get('title') else ''
        elif prop_type == 'rich_text':
            return ' '.join([text.get('plain_text', '') for text in prop_values.get('rich_text', [])]) if prop_values.get('rich_text') else ''
        elif prop_type == 'number':
            return prop_values.get('number')
        elif prop_type == 'select':
            try:
                return prop_values.get('select', {}).get('name')
            except:
                return ' '
        elif prop_type == 'multi_select':
            return [select.get('name') for select in prop_values.get('multi_select', [])] if prop_values.get('multi_select') else []
        elif prop_type == 'date':
            return prop_values.get('date', {}).get('start')
        elif prop_type == 'checkbox':
            return prop_values.get('checkbox')
        elif prop_type == 'url':
            return prop_values.get('url')
        elif prop_type == 'email':
            return prop_values.get('email')
        elif prop_type == 'phone_number':
            return prop_values.get('phone_number')
        elif prop_type == 'formula':
            if prop_values.get('formula', {}).get('string') != None:
                return prop_values.get('formula', {}).get('string')
            else:
                return prop_values.get('formula', {}).get('number')
        elif prop_type == 'relation':
            return [relation.get('id') for relation in prop_values.get('relation', [])] if prop_values.get('relation') else []
        elif prop_type == 'rollup':
            return prop_values.get('rollup', {}).get('array')
        else:
            return None
        

if __name__ == "__main__":
    database = Decoder()
    database.sanatize_settings([1,4])
    tert = database.get_eeprom_setting("",random=True)
    print(tert)