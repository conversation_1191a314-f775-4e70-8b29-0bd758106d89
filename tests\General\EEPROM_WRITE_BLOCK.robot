*** Settings ***
Resource    ${EXECDIR}/resources/serial_utility.resource

*** Variables ***
# EEPROM Setting Payload:
# [BRDH] [BRDL] [ADDRH] [ADDRL] [SIZE] [DATA0] [DATA1] [DATA2] [DATA3]

# Test payload that has a non-existent board ID - no board of id 0x4545
@{BAD_BOARD_ID}=    69    69    1    1    1    0

# Test payload that has an invalid byte size - expecting size of 1, 2, or 4 bytes but gets 16
@{BAD_MEMORY_SIZE}=    255    255    0    48    16    0    0    0    0

# Test payload that has an invalid byte size - expecting 4 bytes but only gets 1
@{BAD_MEMORY_LENGTH}=    255    255    0    48    4    0

*** Test Cases ***
Test Write Zero Settings in Block
    [Timeout]    30 seconds
    [Tags]    EEPROM
    # Send Block Write Request and wait for ACK
    Send Message     WRITE_EEPROM_BLOCK_REQ
    ${response}=    Await Message    WRITE_EEPROM_BLOCK_ACK
    Log    WRITE_EEPROM_BLOCK_ACK In que

    # Send Block Write Done and wait for <PERSON><PERSON> and restart
    Send Message     WRITE_EEPROM_BLOCK_DONE
    ${response}=    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK
    Log    WRITE_EEPROM_BLOCK_DONE_ACK In que

    # Wait for master to finish boot sequence
    ${response}=    Await Message    HEARTBEAT_REPLY
    Log    WRITE_EEPROM_BLOCK_ACK In que

Test Write One Setting in Block
    [Timeout]    30 seconds
    [Tags]    EEPROM
    # Send Block Write Request and wait for ACK
    Send Message     WRITE_EEPROM_BLOCK_REQ
    Await Message    WRITE_EEPROM_BLOCK_ACK

    # Send one setting
    ${WRITE_NAK}=    Write Eeprom Setting    Ingredient Mixing Enable - A6    1
    Should Be True    not ${WRITE_NAK}

    # Send Block Write Done and wait for ACK and restart
    Send Message     WRITE_EEPROM_BLOCK_DONE
    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK

    # Wait for master to finish boot sequence
    Await Message    HEARTBEAT_REPLY

Test Write Many Good Settings in Block
    [Timeout]    60 seconds
    [Tags]    EEPROM
    # Send Block Write Request and wait for ACK
    Send Message     WRITE_EEPROM_BLOCK_REQ
    Await Message    WRITE_EEPROM_BLOCK_ACK

    # Send Block Write Done and wait for ACK and restart
    Send Message     WRITE_EEPROM_BLOCK_DONE
    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK

    # Wait for master to finish boot sequence
    Await Message    HEARTBEAT_REPLY

Test All Failed Settings in Block
    [Timeout]    30 seconds
    [Tags]    EEPROM
    Send Message     WRITE_EEPROM_BLOCK_REQ
    Await Message    WRITE_EEPROM_BLOCK_ACK

    # Attempt to Write setting
    Send Message    WRITE_EEPROM_SETTING    @{BAD_MEMORY_LENGTH}
    # Verify Write NAK
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK

    # Attempt to Write setting
    Send Message    WRITE_EEPROM_SETTING    @{BAD_MEMORY_SIZE}
    # Verify Write NAK
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK

    # Attempt to Write setting
    Send Message    WRITE_EEPROM_SETTING    @{BAD_BOARD_ID}
    # Verify Write NAK
    ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK

    # Send Block Write Done and wait for ACK and restart
    Send Message     WRITE_EEPROM_BLOCK_DONE
    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK

    # Wait for master to finish boot sequence
    Await Message    HEARTBEAT_REPLY

Test Some Failed Settings in Block
    [Timeout]    60 seconds
    [Tags]    EEPROM
    # Send Block Write Request and wait for ACK
    Send Message     WRITE_EEPROM_BLOCK_REQ
    Await Message    WRITE_EEPROM_BLOCK_ACK

    FOR    ${index}    IN RANGE    100
        # Generate random value between 0 and 100
        ${random_value}=    Evaluate    random.randint(0, 100)    modules=random
        IF    ${random_value} < 10
            # Attempt to Write setting
            Send Message    WRITE_EEPROM_SETTING    @{BAD_BOARD_ID}
            # Verify Write NAK
            ${packet}=    Await Message    WRITE_EEPROM_SETTING_NAK
        ELSE
            ${SETTING_NAME}    ${SETTING_DEFAULT}=    Get Random Setting
            ${WRITE_NAK}=    Write Eeprom Setting    ${SETTING_NAME}    ${SETTING_DEFAULT}
        END
    END
    
    # Send Block Write Done and wait for ACK and restart
    Send Message     WRITE_EEPROM_BLOCK_DONE
    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK

    # Wait for master to finish boot sequence
    Await Message    HEARTBEAT_REPLY

Test Write Same Setting in Block Multiple Times
    [Timeout]    60 seconds
    [Tags]    EEPROM
    # Send Block Write Request and wait for ACK
    Send Message     WRITE_EEPROM_BLOCK_REQ
    Await Message    WRITE_EEPROM_BLOCK_ACK

    # Write the same setting 300 times
    FOR    ${index}    IN RANGE    300
        ${WRITE_NAK}=    Write Eeprom Setting    Ingredient Mixing Enable - A6    1
        Should Be True    not ${WRITE_NAK}
    END

    # Send Block Write Done and wait for ACK and restart
    Send Message     WRITE_EEPROM_BLOCK_DONE
    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK

    # Wait for master to finish boot sequence
    Await Message    HEARTBEAT_REPLY

Test No Block Write Done
    [Timeout]    30 seconds
    [Tags]    EEPROM

    # Send Block Write Request and wait for ACK
    Send Message     WRITE_EEPROM_BLOCK_REQ
    Await Message    WRITE_EEPROM_BLOCK_ACK

    # Set a random setting to default
    ${SETTING_NAME}    ${SETTING_DEFAULT}=    Get Random Setting
    ${WRITE_NAK}=    Write Eeprom Setting    ${SETTING_NAME}    ${SETTING_DEFAULT}
    Should Be True    not ${WRITE_NAK}
    
    # Intentionally do not send WRITE_EEPROM_BLOCK_DONE and verify timeout
    Await Message    WRITE_EEPROM_BLOCK_DONE_ACK

    # Wait for master to finish boot sequence
    Await Message    HEARTBEAT_REPLY